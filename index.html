<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> - Développeur Web</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #0a0a0a;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* 3D Background Canvas - This creates the animated background using Three.js */
        #three-canvas {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            pointer-events: none;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            position: relative;
            z-index: 1;
        }

        /* Enhanced Header Section with 3D Profile */
        .header {
            background: rgba(15, 15, 25, 0.9);
            backdrop-filter: blur(20px); /* Creates the frosted glass effect */
            border-radius: 30px;
            padding: 50px;
            margin-bottom: 40px;
            box-shadow: 
                0 25px 50px rgba(0, 0, 0, 0.5),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            text-align: center;
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(100, 255, 218, 0.1);
        }

        /* Animated background particles using CSS gradients */
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 20%, rgba(100, 255, 218, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(255, 100, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(100, 150, 255, 0.1) 0%, transparent 50%);
            animation: floatingBg 20s ease-in-out infinite;
        }

        @keyframes floatingBg {
            0%, 100% { transform: translateX(0) translateY(0) rotate(0deg); }
            33% { transform: translateX(-20px) translateY(-10px) rotate(2deg); }
            66% { transform: translateX(10px) translateY(-20px) rotate(-1deg); }
        }

        /* 3D Profile Container - This is where the magic happens for the rotating cube */
        .profile-container {
            position: relative;
            width: 200px;
            height: 200px;
            margin: 0 auto 30px;
            perspective: 1000px; /* Essential for 3D transforms */
        }

        .profile-3d {
            width: 100%;
            height: 100%;
            position: relative;
            transform-style: preserve-3d; /* Keeps children in 3D space */
            animation: rotate3d 10s linear infinite;
        }

        @keyframes rotate3d {
            0% { transform: rotateX(15deg) rotateY(0deg); }
            100% { transform: rotateX(15deg) rotateY(360deg); }
        }

        /* Each face of the 3D cube positioned in 3D space */
        .profile-face {
            position: absolute;
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: linear-gradient(135deg, #64ffda, #ff64ff, #6496ff);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 60px;
            color: white;
            font-weight: bold;
            box-shadow: 0 0 40px rgba(100, 255, 218, 0.5);
            border: 3px solid rgba(255, 255, 255, 0.2);
        }

        /* Positioning each face of the cube in 3D space */
        .profile-face.front { transform: translateZ(50px); }
        .profile-face.back { transform: translateZ(-50px) rotateY(180deg); }
        .profile-face.left { transform: rotateY(-90deg) translateZ(50px); }
        .profile-face.right { transform: rotateY(90deg) translateZ(50px); }
        .profile-face.top { transform: rotateX(90deg) translateZ(50px); }
        .profile-face.bottom { transform: rotateX(-90deg) translateZ(50px); }

        /* Floating elements around profile using CSS animations */
        .floating-element {
            position: absolute;
            background: rgba(100, 255, 218, 0.2);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .floating-element:nth-child(1) {
            width: 20px;
            height: 20px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-element:nth-child(2) {
            width: 15px;
            height: 15px;
            top: 60%;
            right: 15%;
            animation-delay: 2s;
        }

        .floating-element:nth-child(3) {
            width: 25px;
            height: 25px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) scale(1); opacity: 0.7; }
            50% { transform: translateY(-20px) scale(1.2); opacity: 1; }
        }

        /* Gradient text effects using background-clip */
        .header h1 {
            font-size: 3em;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #64ffda, #ff64ff, #6496ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            z-index: 2;
            text-shadow: 0 0 30px rgba(100, 255, 218, 0.5);
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { filter: drop-shadow(0 0 20px rgba(100, 255, 218, 0.5)); }
            to { filter: drop-shadow(0 0 40px rgba(100, 255, 218, 0.8)); }
        }

        .header p {
            font-size: 1.4em;
            color: #64ffda;
            margin-bottom: 30px;
            position: relative;
            z-index: 2;
            text-shadow: 0 0 10px rgba(100, 255, 218, 0.3);
        }

        .contact-info {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 20px;
            position: relative;
            z-index: 2;
        }

        /* Interactive contact items with hover effects */
        .contact-item {
            display: flex;
            align-items: center;
            gap: 10px;
            background: rgba(15, 15, 25, 0.8);
            padding: 12px 20px;
            border-radius: 30px;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border: 1px solid rgba(100, 255, 218, 0.2);
            backdrop-filter: blur(10px);
        }

        .contact-item:hover {
            background: rgba(100, 255, 218, 0.1);
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 15px 30px rgba(100, 255, 218, 0.3);
            border-color: rgba(100, 255, 218, 0.5);
        }

        /* Enhanced Navigation with 3D effects */
        .nav {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 40px;
            flex-wrap: wrap;
            perspective: 1000px;
        }

        .nav-btn {
            background: rgba(15, 15, 25, 0.9);
            border: 1px solid rgba(100, 255, 218, 0.3);
            color: #64ffda;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            font-weight: 600;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
            position: relative;
            overflow: hidden;
            transform-style: preserve-3d;
        }

        /* Sweep effect using pseudo-elements */
        .nav-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(100, 255, 218, 0.2), transparent);
            transition: all 0.6s ease;
        }

        .nav-btn:hover::before {
            left: 100%;
        }

        .nav-btn:hover, .nav-btn.active {
            background: linear-gradient(135deg, rgba(100, 255, 218, 0.2), rgba(255, 100, 255, 0.2));
            color: white;
            transform: translateY(-5px) rotateX(10deg);
            box-shadow: 
                0 15px 35px rgba(100, 255, 218, 0.4),
                0 0 20px rgba(100, 255, 218, 0.2);
            border-color: rgba(100, 255, 218, 0.8);
        }

        /* Enhanced Content Sections with 3D cards */
        .section {
            background: rgba(15, 15, 25, 0.9);
            backdrop-filter: blur(20px);
            border-radius: 30px;
            padding: 50px;
            margin-bottom: 40px;
            box-shadow: 
                0 25px 50px rgba(0, 0, 0, 0.5),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            display: none;
            animation: slideInUp 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border: 1px solid rgba(100, 255, 218, 0.1);
            position: relative;
            overflow: hidden;
        }

        .section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 10% 10%, rgba(100, 255, 218, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 90% 90%, rgba(255, 100, 255, 0.05) 0%, transparent 50%);
            pointer-events: none;
        }

        .section.active {
            display: block;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px) rotateX(-10deg);
            }
            to {
                opacity: 1;
                transform: translateY(0) rotateX(0deg);
            }
        }

        .section h2 {
            font-size: 2.5em;
            margin-bottom: 30px;
            background: linear-gradient(135deg, #64ffda, #ff64ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            z-index: 2;
            text-align: center;
            text-shadow: 0 0 20px rgba(100, 255, 218, 0.3);
        }

        .section h2::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 3px;
            background: linear-gradient(135deg, #64ffda, #ff64ff);
            border-radius: 2px;
            box-shadow: 0 0 10px rgba(100, 255, 218, 0.5);
        }

        /* 3D Skills Grid */
        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 40px;
            perspective: 1000px;
        }

        .skill-category {
            background: rgba(15, 15, 25, 0.8);
            padding: 30px;
            border-radius: 20px;
            border: 1px solid rgba(100, 255, 218, 0.2);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            transform-style: preserve-3d;
            position: relative;
            overflow: hidden;
        }

        .skill-category::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at center, rgba(100, 255, 218, 0.1) 0%, transparent 70%);
            opacity: 0;
            transition: opacity 0.4s ease;
        }

        .skill-category:hover::before {
            opacity: 1;
        }

        .skill-category:hover {
            transform: translateY(-10px) rotateX(5deg) rotateY(5deg);
            box-shadow: 
                0 20px 40px rgba(100, 255, 218, 0.3),
                0 0 20px rgba(100, 255, 218, 0.2);
            border-color: rgba(100, 255, 218, 0.5);
        }

        .skill-category h3 {
            color: #64ffda;
            margin-bottom: 20px;
            font-size: 1.4em;
            position: relative;
            z-index: 2;
        }

        .skill-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            position: relative;
            z-index: 2;
        }

        .skill-tag {
            background: linear-gradient(135deg, rgba(100, 255, 218, 0.2), rgba(255, 100, 255, 0.2));
            color: white;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 0.9em;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border: 1px solid rgba(100, 255, 218, 0.3);
            backdrop-filter: blur(10px);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .skill-tag::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: all 0.6s ease;
        }

        .skill-tag:hover::before {
            left: 100%;
        }

        .skill-tag:hover {
            background: linear-gradient(135deg, #64ffda, #ff64ff);
            transform: translateY(-3px) scale(1.05) rotateZ(2deg);
            box-shadow: 0 10px 20px rgba(100, 255, 218, 0.4);
        }

        /* 3D Timeline */
        .timeline {
            position: relative;
            padding-left: 40px;
            perspective: 1000px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 20px;
            top: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(180deg, #64ffda, #ff64ff, #6496ff);
            border-radius: 2px;
            box-shadow: 0 0 10px rgba(100, 255, 218, 0.5);
        }

        .timeline-item {
            position: relative;
            margin-bottom: 40px;
            background: rgba(15, 15, 25, 0.8);
            padding: 25px;
            border-radius: 20px;
            border: 1px solid rgba(100, 255, 218, 0.2);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            transform-style: preserve-3d;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -45px;
            top: 30px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: linear-gradient(135deg, #64ffda, #ff64ff);
            box-shadow: 0 0 15px rgba(100, 255, 218, 0.7);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.2); opacity: 0.8; }
        }

        .timeline-item:hover {
            transform: translateX(10px) rotateY(5deg);
            box-shadow: 0 15px 30px rgba(100, 255, 218, 0.3);
            border-color: rgba(100, 255, 218, 0.5);
        }

        .timeline-date {
            font-weight: bold;
            color: #64ffda;
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .timeline-title {
            font-size: 1.3em;
            font-weight: 600;
            margin-bottom: 10px;
            color: white;
        }

        .timeline-item p {
            color: #ccc;
            line-height: 1.6;
        }

        /* 3D Projects Grid */
        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            perspective: 1000px;
        }

        .project-card {
            background: rgba(15, 15, 25, 0.8);
            padding: 30px;
            border-radius: 20px;
            border: 1px solid rgba(100, 255, 218, 0.2);
            transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
            transform-style: preserve-3d;
            cursor: pointer;
        }

        .project-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(100, 255, 218, 0.1), transparent);
            transition: all 0.8s ease;
        }

        .project-card:hover::before {
            left: 100%;
        }

        .project-card:hover {
            transform: translateY(-15px) rotateX(10deg) rotateY(5deg);
            box-shadow: 
                0 25px 50px rgba(100, 255, 218, 0.3),
                0 0 30px rgba(100, 255, 218, 0.2);
            border-color: rgba(100, 255, 218, 0.6);
        }

        .project-card h3 {
            color: #64ffda;
            margin-bottom: 15px;
            font-size: 1.4em;
            position: relative;
            z-index: 2;
        }

        .project-card p {
            color: #ccc;
            line-height: 1.6;
            position: relative;
            z-index: 2;
        }

        /* Enhanced Languages Section */
        .languages {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            perspective: 1000px;
        }

        .language-item {
            text-align: center;
            padding: 25px;
            background: rgba(15, 15, 25, 0.8);
            border-radius: 20px;
            border: 1px solid rgba(100, 255, 218, 0.2);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            transform-style: preserve-3d;
        }

        .language-item:hover {
            transform: translateY(-10px) rotateX(10deg);
            box-shadow: 0 20px 40px rgba(100, 255, 218, 0.3);
            border-color: rgba(100, 255, 218, 0.5);
        }

        .language-name {
            font-weight: bold;
            color: #64ffda;
            margin-bottom: 10px;
            font-size: 1.2em;
        }

        .language-item p {
            color: #ccc;
            margin-bottom: 15px;
        }

        .progress-bar {
            width: 100%;
            height: 10px;
            background: rgba(100, 255, 218, 0.1);
            border-radius: 5px;
            overflow: hidden;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #64ffda, #ff64ff);
            border-radius: 5px;
            transition: all 1s ease;
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .header {
                padding: 30px 20px;
            }
            
            .header h1 {
                font-size: 2.2em;
            }
            
            .profile-container {
                width: 150px;
                height: 150px;
            }
            
            .profile-face {
                width: 150px;
                height: 150px;
                font-size: 45px;
            }
            
            .contact-info {
                flex-direction: column;
                align-items: center;
            }
            
            .nav {
                gap: 10px;
            }
            
            .nav-btn {
                padding: 12px 20px;
                font-size: 0.9em;
            }
            
            .section {
                padding: 30px 20px;
            }
            
            .skills-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .projects-grid {
                grid-template-columns: 1fr;
            }
        }

        /* About section text styling */
        .about-text {
            font-size: 1.2em;
            line-height: 1.8;
            text-align: justify;
            color: #ccc;
            position: relative;
            z-index: 2;
            margin-bottom: 20px;
        }

        /* Scrollbar styling */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(15, 15, 25, 0.5);
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(180deg, #64ffda, #ff64ff);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(180deg, #ff64ff, #64ffda);
        }
    </style>
</head>
<body>
    <!-- 3D Background Canvas -->
    <canvas id="three-canvas"></canvas>

    <!-- Add JavaScript for section navigation -->
    <script>
        function showSection(sectionId) {
            // Hide all sections
            document.querySelectorAll('.section').forEach(section => {
                section.classList.remove('active');
            });
            
            // Show the selected section
            document.getElementById(sectionId).classList.add('active');
            
            // Update active button
            document.querySelectorAll('.nav-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`.nav-btn[onclick="showSection('${sectionId}')"]`).classList.add('active');
        }
    </script>

    <div class="container">
        <!-- Enhanced Header Section -->
        <div class="header">
            <div class="floating-element"></div>
            <div class="floating-element"></div>
            <div class="floating-element"></div>
            
            <div class="profile-container">
                <div class="profile-3d">
                    <div class="profile-face front">HE</div>
                    <div class="profile-face back">DEV</div>
                    <div class="profile-face left">WEB</div>
                    <div class="profile-face right">3D</div>
                    <div class="profile-face top">PRO</div>
                    <div class="profile-face bottom">2024</div>
                </div>
            </div>
            
            <h1>Hamza Elhani</h1>
            <p>Développeur Web Full Stack • Expert 3D</p>
            <div class="contact-info">
                <div class="contact-item">
                    <span>📍</span>
                    <span>Ain Aouda, Hay Ghizlan N° 61</span>
                </div>
                <div class="contact-item">
                    <span>📞</span>
                    <span>+212 601401680</span>
                </div>
                <div class="contact-item">
                    <span>✉️</span>
                    <span><EMAIL></span>
                </div>
                <div class="contact-item">
                    <span>🎂</span>
                    <span>01/01/2002</span>
                </div>
            </div>
        </div>

        <!-- Enhanced Navigation -->
        <div class="nav">
            <button class="nav-btn active" onclick="showSection('about')">À Propos</button>
            <button class="nav-btn" onclick="showSection('experience')">Expérience</button>
            <button class="nav-btn" onclick="showSection('education')">Formation</button>
            <button class="nav-btn" onclick="showSection('skills')">Compétences</button>
            <button class="nav-btn" onclick="showSection('projects')">Projets</button>
            <button class="nav-btn" onclick="showSection('languages')">Langues</button>
        </div>

        <!-- About Section -->
        <div class="section active" id="about">
            <h2>À Propos de Moi</h2>
            <p class="about-text">
                Jeune diplômé en Développement Informatique avec une passion pour les technologies 3D et les interfaces immersives. Je suis à la recherche d'une expérience professionnelle pour améliorer et approfondir mes compétences techniques et personnelles. Motivé, curieux et passionné par les nouvelles technologies, je suis prêt à relever des défis et à contribuer activement à des projets innovants.
            </p>
            <p class="about-text">
                Mon profil combine des compétences techniques solides en développement web, programmation 3D et design interactif avec des qualités personnelles qui me permettent de m'adapter rapidement aux environnements de travail dynamiques. Je privilégie le travail en équipe, la communication efficace et l'apprentissage continu des technologies émergentes.
            </p>
        </div>

        <!-- Experience Section -->
        <div class="section" id="experience">
            <h2>Expérience Professionnelle</h2>
            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-date">2024</div>
                    <div class="timeline-title">Stage - Hôpital des Spécialités</div>
                    <p>Stage de 3 mois dans le domaine informatique hospitalier. Développement et maintenance de solutions numériques avancées pour améliorer la gestion des données patients et l'efficacité des services avec des interfaces 3D intuitives.</p>
                </div>
                <div class="timeline-item">
                    <div class="timeline-date">2023</div>
                    <div class="timeline-title">Stage - Assurance April</div>
                    <p>Stage de 2 mois axé sur le développement d'applications web modernes pour la gestion des polices d'assurance avec des tableaux de bord interactifs et des visualisations de données en temps réel.</p>
                </div>
                <div class="timeline-item">
                    <div class="timeline-date">2022</div>
                    <div class="timeline-title">Projet Personnel Innovation</div>
                    <p>Réalisation d'un projet complet sous le thème : Création d'un site web
